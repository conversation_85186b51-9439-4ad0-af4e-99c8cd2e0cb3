#!/bin/bash

echo "========================================"
echo "   Настройка базы данных TravelGuide"
echo "========================================"
echo

echo "Проверяем установку .NET..."
if ! command -v dotnet &> /dev/null; then
    echo "ОШИБКА: .NET SDK не установлен!"
    echo "Скачайте и установите .NET 8 SDK с: https://dotnet.microsoft.com/download/dotnet/8.0"
    exit 1
fi
echo "✓ .NET SDK установлен"

echo
echo "Устанавливаем Entity Framework Tools..."
dotnet tool install --global dotnet-ef --skip-existing
if [ $? -ne 0 ]; then
    echo "ОШИБКА: Не удалось установить EF Tools"
    exit 1
fi
echo "✓ EF Tools установлены"

echo
echo "Восстанавливаем пакеты NuGet..."
dotnet restore
if [ $? -ne 0 ]; then
    echo "ОШИБКА: Не удалось восстановить пакеты"
    exit 1
fi
echo "✓ Пакеты восстановлены"

echo
echo "Применяем миграции к базе данных..."
echo "ВАЖНО: Если увидите сообщение 'fail: Microsoft.EntityFrameworkCore.Database.Connection' - это НЕ ошибка!"
echo "Это просто означает, что база данных создается заново."
echo
dotnet ef database update
if [ $? -ne 0 ]; then
    echo "ОШИБКА: Не удалось применить миграции"
    echo
    echo "Возможные причины:"
    echo "1. PostgreSQL не запущен"
    echo "2. Неправильный пароль в appsettings.json"
    echo "3. База данных недоступна"
    echo
    echo "Проверьте настройки в appsettings.json и убедитесь, что PostgreSQL запущен"
    exit 1
fi
echo "✓ Миграции применены успешно"

echo
echo "========================================"
echo "   Настройка завершена успешно!"
echo "========================================"
echo
echo "Для запуска приложения выполните:"
echo "  dotnet run"
echo
echo "Затем откройте браузер и перейдите по адресу:"
echo "  http://localhost:5000"
echo
