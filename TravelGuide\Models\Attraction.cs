using System.ComponentModel.DataAnnotations;

namespace TravelGuide.Models
{
    public class Attraction
    {
        public int Id { get; set; }

        [Display(Name = "Название")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Описание")]
        public string Description { get; set; } = string.Empty; // Краткое описание

        [Display(Name = "История")]
        public string History { get; set; } = string.Empty; // Полная история

        [Display(Name = "URL фото")]
        public string? PhotoUrl { get; set; } // URL фото

        [Display(Name = "Часы работы")]
        public string? WorkingHours { get; set; } // Часы работы

        [Display(Name = "Стоимость посещения")]
        public decimal? AdmissionCost { get; set; } // Стоимость посещения

        public int CityId { get; set; } // Внешний ключ

        [Display(Name = "Город")]
        public City? City { get; set; } // Навигационное свойство
    }
}