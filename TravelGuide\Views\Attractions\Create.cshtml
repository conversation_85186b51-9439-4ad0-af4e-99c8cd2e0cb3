@model TravelGuide.Models.Attraction

@{
    ViewData["Title"] = "Создание достопримечательности";
}

<h1>Создание</h1>

<h4>Достопримечательность</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Description" class="control-label"></label>
                <textarea asp-for="Description" class="form-control" rows="3"></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="History" class="control-label"></label>
                <textarea asp-for="History" class="form-control" rows="5"></textarea>
                <span asp-validation-for="History" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PhotoUrl" class="control-label"></label>
                <input asp-for="PhotoUrl" class="form-control" />
                <span asp-validation-for="PhotoUrl" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="WorkingHours" class="control-label"></label>
                <input asp-for="WorkingHours" class="form-control" />
                <span asp-validation-for="WorkingHours" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="AdmissionCost" class="control-label"></label>
                <input asp-for="AdmissionCost" class="form-control" />
                <span asp-validation-for="AdmissionCost" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CityId" class="control-label"></label>
                <select asp-for="CityId" class ="form-control" asp-items="ViewBag.CityId"></select>
            </div>
            <div class="form-group mt-3">
                <input type="submit" value="Создать" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div class="mt-3">
    <a asp-action="Index" class="btn btn-secondary">Назад к списку</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
