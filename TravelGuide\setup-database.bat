@echo off
echo ========================================
echo   Настройка базы данных TravelGuide
echo ========================================
echo.

echo Проверяем установку .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ОШИБКА: .NET SDK не установлен!
    echo Скачайте и установите .NET 8 SDK с: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)
echo ✓ .NET SDK установлен

echo.
echo Устанавливаем Entity Framework Tools...
dotnet tool install --global dotnet-ef --skip-existing
if %errorlevel% neq 0 (
    echo ОШИБКА: Не удалось установить EF Tools
    pause
    exit /b 1
)
echo ✓ EF Tools установлены

echo.
echo Восстанавливаем пакеты NuGet...
dotnet restore
if %errorlevel% neq 0 (
    echo ОШИБКА: Не удалось восстановить пакеты
    pause
    exit /b 1
)
echo ✓ Пакеты восстановлены

echo.
echo Применяем миграции к базе данных...
dotnet ef database update
if %errorlevel% neq 0 (
    echo ОШИБКА: Не удалось применить миграции
    echo.
    echo Возможные причины:
    echo 1. PostgreSQL не запущен
    echo 2. Неправильный пароль в appsettings.json
    echo 3. База данных недоступна
    echo.
    echo Проверьте настройки в appsettings.json и убедитесь, что PostgreSQL запущен
    pause
    exit /b 1
)
echo ✓ Миграции применены успешно

echo.
echo ========================================
echo   Настройка завершена успешно!
echo ========================================
echo.
echo Для запуска приложения выполните:
echo   dotnet run
echo.
echo Затем откройте браузер и перейдите по адресу:
echo   http://localhost:5000
echo.
pause
