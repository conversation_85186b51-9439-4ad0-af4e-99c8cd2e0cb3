@model TravelGuide.Models.City

@{
    ViewData["Title"] = "Редактирование города";
}

<h1>Редактирование</h1>

<h4>Город</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <div class="form-group">
                <label asp-for="Name" class="control-label"></label>
                <input asp-for="Name" class="form-control" />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Region" class="control-label"></label>
                <input asp-for="Region" class="form-control" />
                <span asp-validation-for="Region" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Population" class="control-label"></label>
                <input asp-for="Population" class="form-control" />
                <span asp-validation-for="Population" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="History" class="control-label"></label>
                <textarea asp-for="History" class="form-control" rows="5"></textarea>
                <span asp-validation-for="History" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CoatOfArmsUrl" class="control-label"></label>
                <input asp-for="CoatOfArmsUrl" class="form-control" />
                <span asp-validation-for="CoatOfArmsUrl" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PhotoUrl" class="control-label"></label>
                <input asp-for="PhotoUrl" class="form-control" />
                <span asp-validation-for="PhotoUrl" class="text-danger"></span>
            </div>
            <div class="form-group mt-3">
                <input type="submit" value="Сохранить" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div class="mt-3">
    <a asp-action="Index" class="btn btn-secondary">Назад к списку</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
