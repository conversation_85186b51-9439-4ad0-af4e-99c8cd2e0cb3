-- Скрипт для ручного создания базы данных TravelGuide
-- Используйте этот скрипт, если автоматическая настройка не работает

-- 1. Создание базы данных (выполните от имени суперпользователя postgres)
CREATE DATABASE "TravelGuideDb"
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'Russian_Russia.1251'
    LC_CTYPE = 'Russian_Russia.1251'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- 2. Подключитесь к базе данных TravelGuideDb и выполните следующие команды:

-- Создание таблицы Cities
CREATE TABLE "Cities" (
    "Id" SERIAL PRIMARY KEY,
    "Name" TEXT NOT NULL,
    "Region" TEXT NOT NULL,
    "Population" INTEGER NOT NULL,
    "History" TEXT NOT NULL,
    "CoatOfArmsUrl" TEXT,
    "PhotoUrl" TEXT
);

-- Создание таблицы Attractions
CREATE TABLE "Attractions" (
    "Id" SERIAL PRIMARY KEY,
    "Name" TEXT NOT NULL,
    "Description" TEXT NOT NULL,
    "History" TEXT NOT NULL,
    "PhotoUrl" TEXT,
    "WorkingHours" TEXT,
    "AdmissionCost" DECIMAL,
    "CityId" INTEGER NOT NULL,
    CONSTRAINT "FK_Attractions_Cities_CityId" FOREIGN KEY ("CityId") 
        REFERENCES "Cities" ("Id") ON DELETE CASCADE
);

-- Создание индекса для внешнего ключа
CREATE INDEX "IX_Attractions_CityId" ON "Attractions" ("CityId");

-- 3. Заполнение данными

-- Добавление городов
INSERT INTO "Cities" ("Name", "Region", "Population", "History", "CoatOfArmsUrl", "PhotoUrl") VALUES
('Лондон', 'Большой Лондон', 9002488, 'Основан римлянами как Лондиниум, столица Великобритании.', '/images/london_coa.png', '/images/london_city.jpg'),
('Париж', 'Иль-де-Франс', 2165423, 'Столица Франции, мировой центр искусства, моды и культуры.', '/images/paris_coa.png', '/images/paris_city.jpg');

-- Добавление достопримечательностей
INSERT INTO "Attractions" ("Name", "Description", "History", "PhotoUrl", "WorkingHours", "AdmissionCost", "CityId") VALUES
('Британский музей', 'Один из крупнейших музеев мира.', 'Основан в 1753 году, содержит обширную коллекцию мирового искусства и артефактов.', '/images/british_museum.jpg', '10:00–17:00', NULL, 1),
('Лондонский Тауэр', 'Историческая крепость на северном берегу Темзы.', 'Основан Вильгельмом Завоевателем в 1066 году.', '/images/tower_of_london.jpg', '09:00–17:30 (Вт-Сб), 10:00–17:30 (Вс-Пн)', 33.60, 1),
('Лувр', 'Один из крупнейших и самый популярный художественный музей мира.', 'Бывший королевский дворец, музей с 1793 года.', '/images/louvre.jpg', '09:00–18:00 (Пн, Ср, Чт, Сб, Вс), 09:00–21:45 (Пт)', 17.00, 2),
('Эйфелева башня', 'Металлическая башня в центре Парижа, символ Франции.', 'Построена Гюставом Эйфелем для Всемирной выставки 1889 года.', '/images/eiffel_tower.jpg', '09:30–22:45', 26.80, 2);

-- Создание таблицы для отслеживания миграций (чтобы EF Core понимал, что миграции применены)
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" VARCHAR(150) NOT NULL,
    "ProductVersion" VARCHAR(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);

-- Добавление записи о примененной миграции
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion") VALUES
('20250417104145_InitialCreatePostgres', '9.0.4');

-- Проверка данных
SELECT 'Города:' as info;
SELECT "Id", "Name", "Region", "Population" FROM "Cities";

SELECT 'Достопримечательности:' as info;
SELECT a."Id", a."Name", c."Name" as "City", a."AdmissionCost" 
FROM "Attractions" a 
JOIN "Cities" c ON a."CityId" = c."Id";
