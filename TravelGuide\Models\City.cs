using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace TravelGuide.Models
{
    public class City
    {
        public int Id { get; set; }

        [Display(Name = "Название")]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Регион")]
        public string Region { get; set; } = string.Empty;

        [Display(Name = "Население")]
        public int Population { get; set; }

        [Display(Name = "История")]
        public string History { get; set; } = string.Empty;

        [Display(Name = "URL герба")]
        public string? CoatOfArmsUrl { get; set; } // URL герба

        [Display(Name = "URL фото")]
        public string? PhotoUrl { get; set; } // URL фото города

        [Display(Name = "Достопримечательности")]
        public List<Attraction> Attractions { get; set; } = new List<Attraction>();
    }
}