# 🚀 Инструкция по запуску TravelGuide для друга

## Что это за проект?
Это веб-приложение "Туристический путеводитель" с информацией о городах и достопримечательностях. В нем уже есть данные о Лондоне, Париже и их достопримечательностях.

## 📋 Что нужно установить

### 1. PostgreSQL (База данных)
- **Скачать**: https://www.postgresql.org/download/
- **Установка**: Выберите все настройки по умолчанию
- **ВАЖНО**: Запомните пароль для пользователя `postgres`!

### 2. .NET 8 SDK
- **Скачать**: https://dotnet.microsoft.com/download/dotnet/8.0
- **Установка**: Выберите все настройки по умолчанию

## 🔧 Настройка проекта

### Способ 1: Автоматическая настройка (рекомендуется)

#### Для Windows:
1. Откройте файл `appsettings.json`
2. Найдите строку `"Password=12345678"`
3. Замените `12345678` на ваш пароль от PostgreSQL
4. Сохраните файл
5. Запустите файл `setup-database.bat` (двойной клик)
6. Если все прошло успешно, выполните: `dotnet run`

#### Для Linux/Mac:
1. Откройте файл `appsettings.json`
2. Найдите строку `"Password=12345678"`
3. Замените `12345678` на ваш пароль от PostgreSQL
4. Сохраните файл
5. Откройте терминал в папке проекта
6. Выполните: `./setup-database.sh`
7. Если все прошло успешно, выполните: `dotnet run`

### Способ 2: Ручная настройка

1. **Настройте подключение к базе данных:**
   - Откройте файл `appsettings.json`
   - Замените `Password=12345678` на ваш пароль от PostgreSQL

2. **Откройте командную строку/терминал в папке проекта**

3. **Выполните команды по порядку:**
   ```bash
   # Установка EF Tools
   dotnet tool install --global dotnet-ef
   
   # Восстановление пакетов
   dotnet restore
   
   # Создание базы данных и применение миграций
   dotnet ef database update
   
   # Запуск приложения
   dotnet run
   ```

## 🌐 Открытие приложения

После выполнения `dotnet run`:
1. Откройте браузер
2. Перейдите по адресу: **http://localhost:5000**
3. Вы увидите главную страницу с городами

## 📊 Что вы увидите

В приложении уже есть данные:

### Города:
- **Лондон** (население: 9,002,488)
- **Париж** (население: 2,165,423)

### Достопримечательности:
- **Лондон**: Британский музей, Лондонский Тауэр
- **Париж**: Лувр, Эйфелева башня

## ❗ Возможные проблемы и решения

### Проблема: "dotnet command not found"
**Решение**: Перезапустите командную строку после установки .NET SDK

### Проблема: Ошибка подключения к базе данных
**Решение**:
1. Убедитесь, что PostgreSQL запущен
2. Проверьте пароль в `appsettings.json`
3. Убедитесь, что порт 5432 не заблокирован

### Проблема: "dotnet ef command not found"
**Решение**: Выполните `dotnet tool install --global dotnet-ef`

### Проблема: База данных пустая
**Решение**: Приложение автоматически заполнит базу при первом запуске

### ✅ ВАЖНО: Сообщение "fail: Microsoft.EntityFrameworkCore.Database.Connection" - это НЕ ошибка!
Если вы видите такое сообщение, но в конце написано "Done." - значит все прошло успешно!
Это просто предупреждение о том, что база данных создавалась заново.

## 🔄 Альтернативные настройки

Если у вас другие настройки PostgreSQL, скопируйте файл `appsettings.Example.json` в `appsettings.json` и измените настройки:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=ВАШ_ХОСТ;Port=ВАШ_ПОРТ;Database=ВАШ_БД;Username=ВАШ_ПОЛЬЗОВАТЕЛЬ;Password=ВАШ_ПАРОЛЬ;"
  }
}
```

## 📞 Если ничего не работает

1. Убедитесь, что PostgreSQL запущен (должен быть в трее/системных процессах)
2. Проверьте, что .NET 8 SDK установлен: `dotnet --version`
3. Убедитесь, что вы находитесь в папке с проектом (там где файл `TravelGuide.csproj`)
4. Попробуйте перезапустить компьютер и повторить все заново

**Удачи! 🎉**
