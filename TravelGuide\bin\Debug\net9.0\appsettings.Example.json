{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=TravelGuideDb;Username=********;Password=*******************;", "AlternativeConnection": "Host=localhost;Port=5433;Database=TravelGuideDb;Username=myuser;Password=mypassword;", "DockerConnection": "Host=localhost;Port=5432;Database=TravelGuideDb;Username=********;Password=********;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}