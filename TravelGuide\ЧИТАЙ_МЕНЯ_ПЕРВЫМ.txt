🚀 БЫСТРЫЙ СТАРТ ДЛЯ ДРУГА 🚀

Привет! Твой друг прислал тебе проект "Туристический путеводитель".

📋 ЧТО НУЖНО СДЕЛАТЬ:

1. Установи PostgreSQL: https://www.postgresql.org/download/
   (Запомни пароль для пользователя postgres!)

2. Установи .NET 8 SDK: https://dotnet.microsoft.com/download/dotnet/8.0

3. Открой файл appsettings.json
   Найди строку: "Password=12345678"
   Замени на: "Password=ТВОЙ_ПАРОЛЬ_ОТ_POSTGRES"

4. Для Windows: запусти setup-database.bat
   Для Linux/Mac: запусти ./setup-database.sh

5. Выполни: dotnet run

6. Открой браузер: http://localhost:5000

🎉 ГОТОВО! Приложение работает с данными о Лондоне и Париже!

📖 Подробные инструкции в файле: ИНСТРУКЦИЯ_ДЛЯ_ДРУГА.md

❗ Если что-то не работает:
- Убедись, что PostgreSQL запущен
- Проверь пароль в appsettings.json
- Читай ИНСТРУКЦИЯ_ДЛЯ_ДРУГА.md
