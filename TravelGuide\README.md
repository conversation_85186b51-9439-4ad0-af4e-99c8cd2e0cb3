# TravelGuide - Туристический путеводитель

## 📖 Описание проекта

TravelGuide - это веб-приложение для управления туристической информацией о городах и их достопримечательностях. Приложение позволяет пользователям просматривать, добавлять, редактировать и удалять информацию о городах и связанных с ними достопримечательностях.

## 🛠 Технологии и фреймворки

- **Backend**: ASP.NET Core MVC (.NET 9.0)
- **База данных**: PostgreSQL
- **ORM**: Entity Framework Core 9.0.4
- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **Архитектура**: Model-View-Controller (MVC)

### Основные пакеты NuGet:
- `Microsoft.EntityFrameworkCore.Design` (9.0.4)
- `Microsoft.EntityFrameworkCore.Tools` (9.0.4)
- `Microsoft.VisualStudio.Web.CodeGeneration.Design` (9.0.0)
- `Npgsql.EntityFrameworkCore.PostgreSQL` (9.0.4)

## 📋 Требования к системе

### Минимальные требования:
- **.NET 9.0 SDK** или выше
- **PostgreSQL** 12.0 или выше
- **Visual Studio 2022** или **Visual Studio Code** (рекомендуется)
- **Git** для клонирования репозитория

### Поддерживаемые операционные системы:
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 18.04+, CentOS 7+)

## 🚀 Установка и настройка

### 1. Клонирование репозитория
```bash
git clone https://github.com/your-username/TravelGuide.git
cd TravelGuide
```

### 2. Установка зависимостей
```bash
dotnet restore
```

### 3. Настройка базы данных

#### Установка PostgreSQL:
- **Windows**: Скачайте с [официального сайта PostgreSQL](https://www.postgresql.org/download/windows/)
- **macOS**: `brew install postgresql`
- **Linux**: `sudo apt-get install postgresql postgresql-contrib`

#### Создание базы данных:
```sql
-- Подключитесь к PostgreSQL как суперпользователь
CREATE DATABASE TravelGuideDb;
CREATE USER postgres WITH PASSWORD '12345678';
GRANT ALL PRIVILEGES ON DATABASE TravelGuideDb TO postgres;
```

### 4. Настройка строки подключения

Отредактируйте файл `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Port=5432;Database=TravelGuideDb;Username=postgres;Password=*******************;"
  }
}
```

### 5. Применение миграций
```bash
dotnet ef database update
```

## ▶️ Запуск приложения

### Режим разработки:
```bash
dotnet run
```

### Режим производства:
```bash
dotnet run --environment Production
```

После запуска приложение будет доступно по адресу:
- **HTTP**: http://localhost:5000
- **HTTPS**: https://localhost:5001

## 🎯 Основная функциональность

### 🏙️ Управление городами
- **Просмотр списка городов** с карточками и изображениями
- **Поиск городов** по названию
- **Добавление новых городов** с полной информацией
- **Редактирование** существующих городов
- **Удаление городов** с подтверждением
- **Детальная информация** о городе с отображением достопримечательностей

### 🏛️ Управление достопримечательностями
- **Просмотр всех достопримечательностей** с привязкой к городам
- **Добавление новых достопримечательностей**
- **Редактирование** информации о достопримечательностях
- **Удаление достопримечательностей**
- **Детальная информация** с историей, часами работы и стоимостью

### 📊 Информация о сущностях

#### Город (City):
- Название
- Регион
- Население
- История
- URL герба
- URL фотографии
- Связанные достопримечательности

#### Достопримечательность (Attraction):
- Название
- Описание
- История
- URL фотографии
- Часы работы
- Стоимость посещения
- Привязка к городу

## 📸 Примеры использования

### Главная страница
Отображает карточки всех городов с возможностью поиска:
- Фотографии городов
- Основная информация (название, регион, население)
- Ссылки на детальную информацию

### Страница города
Показывает полную информацию о городе:
- Детальное описание и история
- Герб и фотография города
- Список всех достопримечательностей города

### Управление данными
Полный CRUD интерфейс для:
- Добавления новых городов и достопримечательностей
- Редактирования существующей информации
- Безопасного удаления с подтверждением

## 📁 Структура проекта

```
TravelGuide/
├── Controllers/                 # MVC контроллеры
│   ├── HomeController.cs       # Главная страница и поиск
│   ├── CitiesController.cs     # CRUD операции для городов
│   └── AttractionsController.cs # CRUD операции для достопримечательностей
├── Data/                       # Слой данных
│   ├── ApplicationDbContext.cs # Контекст Entity Framework
│   └── SeedData.cs            # Начальные данные
├── Models/                     # Модели данных
│   ├── City.cs                # Модель города
│   ├── Attraction.cs          # Модель достопримечательности
│   └── ErrorViewModel.cs      # Модель для ошибок
├── Views/                      # Razor представления
│   ├── Home/                  # Представления главной страницы
│   ├── Cities/                # Представления для городов
│   ├── Attractions/           # Представления для достопримечательностей
│   └── Shared/                # Общие представления и макеты
├── wwwroot/                    # Статические файлы
│   ├── css/                   # Стили CSS
│   ├── js/                    # JavaScript файлы
│   ├── images/                # Изображения городов и достопримечательностей
│   └── lib/                   # Библиотеки (Bootstrap, jQuery)
├── Migrations/                 # Миграции Entity Framework
├── Properties/                 # Настройки проекта
├── Program.cs                  # Точка входа приложения
├── TravelGuide.csproj         # Файл проекта
└── appsettings.json           # Конфигурация приложения
```

## 🤝 Как внести вклад в проект

1. **Fork** репозитория
2. Создайте **feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit** ваши изменения: `git commit -m 'Add amazing feature'`
4. **Push** в branch: `git push origin feature/amazing-feature`
5. Откройте **Pull Request**

### Правила разработки:
- Следуйте принципам SOLID
- Пишите unit тесты для новой функциональности
- Используйте осмысленные имена переменных и методов
- Документируйте сложную логику
- Соблюдайте стиль кодирования C#

## 📄 Лицензия

Этот проект распространяется под лицензией MIT. Подробности см. в файле [LICENSE](LICENSE).
## 📚 Дополнительная документация

- [ASP.NET Core Documentation](https://docs.microsoft.com/en-us/aspnet/core/)
- [Entity Framework Core Documentation](https://docs.microsoft.com/en-us/ef/core/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 🔧 Устранение неполадок

### Проблемы с подключением к базе данных:
1. Убедитесь, что PostgreSQL запущен
2. Проверьте правильность строки подключения
3. Убедитесь, что база данных создана
4. Проверьте права доступа пользователя

### Проблемы с миграциями:
```bash
# Удаление и пересоздание миграций
dotnet ef migrations remove
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### Проблемы с зависимостями:
```bash
# Очистка и восстановление пакетов
dotnet clean
dotnet restore
```

---
