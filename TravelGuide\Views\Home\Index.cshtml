﻿@model IEnumerable<TravelGuide.Models.City>

@{
    ViewData["Title"] = "Туристический путеводитель";
}

<div class="text-center">
    <h1 class="display-4">Города</h1>
</div>

<div class="row mb-4">
    <div class="col-md-6 offset-md-3">
        <form asp-action="Index" method="get" class="form-inline">
            <div class="input-group w-100">
                <input type="text" name="searchString" value="@ViewData["CurrentFilter"]" class="form-control" placeholder="Поиск по названию города..." />
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary">Поиск</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    @foreach (var city in Model)
    {
        <div class="col-md-4 mb-4">
            <div class="card">
                @if (!string.IsNullOrEmpty(city.PhotoUrl))
                {
                    <img src="@Url.Content(city.PhotoUrl)" class="card-img-top" alt="Фото @city.Name" style="height: 200px; object-fit: cover;">
                }
                else
                {
                     <img src="/images/placeholder.png" class="card-img-top" alt="Нет фото" style="height: 200px; object-fit: cover;"> // Плейсхолдер, если фото нет
                }
                <div class="card-body">
                    <h5 class="card-title">
                        <a asp-controller="Cities" asp-action="Details" asp-route-id="@city.Id">@city.Name</a>
                    </h5>
                    <p class="card-text">@city.Region</p>
                    <p class="card-text"><small class="text-muted">Население: @city.Population.ToString("N0")</small></p>
                </div>
            </div>
        </div>
    }
</div>
