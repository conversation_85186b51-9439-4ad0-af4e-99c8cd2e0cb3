@model TravelGuide.Models.Attraction

@{
    ViewData["Title"] = Model.Name;
}

<h1>@Model.Name</h1>

<div class="row mb-4">
    <div class="col-md-8">
        <dl class="row">
            <dt class="col-sm-3">
                Описание
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.Description)
            </dd>
            <dt class="col-sm-3">
                История
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.History)
            </dd>
            <dt class="col-sm-3">
                Часы работы
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.WorkingHours)
            </dd>
            <dt class="col-sm-3">
                Стоимость посещения
            </dt>
            <dd class="col-sm-9">
                @if (Model.AdmissionCost.HasValue)
                {
                    @Model.AdmissionCost.Value.ToString("C")
                }
                else
                {
                    <span>Бесплатно</span>
                }
            </dd>
            <dt class="col-sm-3">
                Город
            </dt>
            <dd class="col-sm-9">
                <a asp-controller="Cities" asp-action="Details" asp-route-id="@Model.CityId">
                    @Html.DisplayFor(model => model.City.Name)
                </a>
            </dd>
        </dl>
    </div>
    <div class="col-md-4">
        <div class="card">
            @if (!string.IsNullOrEmpty(Model.PhotoUrl))
            {
                <img src="@Url.Content(Model.PhotoUrl)" class="card-img-top" alt="Фото @Model.Name" style="height: 300px; object-fit: cover;">
            }
            else
            {
                <img src="/images/placeholder.png" class="card-img-top" alt="Нет фото" style="height: 300px; object-fit: cover;">
            }
        </div>
    </div>
</div>

<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id" class="btn btn-primary">Редактировать</a> |
    <a asp-controller="Cities" asp-action="Details" asp-route-id="@Model.CityId" class="btn btn-secondary">Назад к городу</a> |
    <a asp-action="Index" class="btn btn-secondary">Все достопримечательности</a>
</div>
