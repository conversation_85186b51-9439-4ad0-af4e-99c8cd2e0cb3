@model TravelGuide.Models.City

@{
    ViewData["Title"] = Model.Name;
}

<h1>@Model.Name</h1>

<div class="row mb-4">
    <div class="col-md-8">
        <dl class="row">
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.Region)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.Region)
            </dd>
            <dt class="col-sm-3">
                @Html.DisplayNameFor(model => model.Population)
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.Population)
            </dd>
            <dt class="col-sm-3">
                История
            </dt>
            <dd class="col-sm-9">
                @Html.DisplayFor(model => model.History)
            </dd>
        </dl>
    </div>
    <div class="col-md-4">
        <div class="card">
            @if (!string.IsNullOrEmpty(Model.PhotoUrl))
            {
                <img src="@Url.Content(Model.PhotoUrl)" class="card-img-top" alt="Фото @Model.Name" style="height: 200px; object-fit: cover;">
            }
            else
            {
                <img src="/images/placeholder.png" class="card-img-top" alt="Нет фото" style="height: 200px; object-fit: cover;">
            }
            <div class="card-body text-center">
                @if (!string.IsNullOrEmpty(Model.CoatOfArmsUrl))
                {
                    <img src="@Url.Content(Model.CoatOfArmsUrl)" alt="Герб @Model.Name" style="max-height: 100px; max-width: 100%;">
                }
            </div>
        </div>
    </div>
</div>

<h2>Достопримечательности</h2>
<hr />

@if (Model.Attractions != null && Model.Attractions.Any())
{
    <div class="row">
        @foreach (var attraction in Model.Attractions)
        {
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    @if (!string.IsNullOrEmpty(attraction.PhotoUrl))
                    {
                        <img src="@Url.Content(attraction.PhotoUrl)" class="card-img-top" alt="Фото @attraction.Name" style="height: 200px; object-fit: cover;">
                    }
                    else
                    {
                        <img src="/images/placeholder.png" class="card-img-top" alt="Нет фото" style="height: 200px; object-fit: cover;">
                    }
                    <div class="card-body">
                        <h5 class="card-title">
                            <a asp-controller="Attractions" asp-action="Details" asp-route-id="@attraction.Id">@attraction.Name</a>
                        </h5>
                        <p class="card-text">@attraction.Description</p>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <p>Для этого города пока нет достопримечательностей.</p>
}

<div>
    <a asp-action="Edit" asp-route-id="@Model?.Id" class="btn btn-primary">Редактировать</a> |
    <a asp-action="Index" class="btn btn-secondary">Назад к списку</a>
</div>
