@model IEnumerable<TravelGuide.Models.Attraction>

@{
    ViewData["Title"] = "Достопримечательности";
}

<h1>Достопримечательности</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">Добавить новую достопримечательность</a>
</p>

<div class="row">
    @foreach (var item in Model)
    {
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                @if (!string.IsNullOrEmpty(item.PhotoUrl))
                {
                    <img src="@Url.Content(item.PhotoUrl)" class="card-img-top" alt="Фото @item.Name" style="height: 200px; object-fit: cover;">
                }
                else
                {
                    <img src="/images/placeholder.png" class="card-img-top" alt="Нет фото" style="height: 200px; object-fit: cover;">
                }
                <div class="card-body">
                    <h5 class="card-title">@item.Name</h5>
                    <p class="card-text text-truncate">@item.Description</p>
                    <p class="card-text">
                        <small class="text-muted">
                            <strong>Город:</strong>
                            <a asp-controller="Cities" asp-action="Details" asp-route-id="@item.CityId">
                                @(item.City?.Name ?? "Неизвестно")
                            </a>
                        </small>
                    </p>
                    @if (item.AdmissionCost.HasValue)
                    {
                        <p class="card-text"><strong>Стоимость:</strong> @item.AdmissionCost.Value.ToString("C")</p>
                    }
                    else
                    {
                        <p class="card-text"><strong>Стоимость:</strong> Бесплатно</p>
                    }
                </div>
                <div class="card-footer">
                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">Подробнее</a>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">Редактировать</a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">Удалить</a>
                </div>
            </div>
        </div>
    }
</div>
