using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using TravelGuide.Models;

namespace TravelGuide.Data
{
    public static class SeedData
    {
        public static void Initialize(IServiceProvider serviceProvider)
        {
            using (var context = new ApplicationDbContext(
                serviceProvider.GetRequiredService<
                    DbContextOptions<ApplicationDbContext>>()))
            {
                // Проверяем, есть ли уже города в БД
                if (context.Cities.Any())
                {
                    return;   // БД уже заполнена
                }

                var london = new City
                {
                    Name = "Лондон",
                    Region = "Большой Лондон",
                    Population = 9002488,
                    History = "Основан римлянами как Лондиниум, столица Великобритании.",
                    CoatOfArmsUrl = "/images/london_coa.png", // Пути к изображениям - примеры
                    PhotoUrl = "/images/london_city.jpg"
                };

                var paris = new City
                {
                    Name = "Париж",
                    Region = "Иль-де-Франс",
                    Population = 2165423,
                    History = "Столица Франции, мировой центр искусства, моды и культуры.",
                    CoatOfArmsUrl = "/images/paris_coa.png",
                    PhotoUrl = "/images/paris_city.jpg"
                };

                context.Cities.AddRange(london, paris);
                context.SaveChanges(); // Сохраняем города, чтобы получить их ID

                context.Attractions.AddRange(
                    new Attraction
                    {
                        Name = "Британский музей",
                        Description = "Один из крупнейших музеев мира.",
                        History = "Основан в 1753 году, содержит обширную коллекцию мирового искусства и артефактов.",
                        PhotoUrl = "/images/british_museum.jpg",
                        WorkingHours = "10:00–17:00",
                        AdmissionCost = null, // Бесплатно
                        CityId = london.Id // Связываем с Лондоном
                    },
                    new Attraction
                    {
                        Name = "Лондонский Тауэр",
                        Description = "Историческая крепость на северном берегу Темзы.",
                        History = "Основан Вильгельмом Завоевателем в 1066 году.",
                        PhotoUrl = "/images/tower_of_london.jpg",
                        WorkingHours = "09:00–17:30 (Вт-Сб), 10:00–17:30 (Вс-Пн)",
                        AdmissionCost = 33.60m,
                        CityId = london.Id
                    },
                    new Attraction
                    {
                        Name = "Лувр",
                        Description = "Один из крупнейших и самый популярный художественный музей мира.",
                        History = "Бывший королевский дворец, музей с 1793 года.",
                        PhotoUrl = "/images/louvre.jpg",
                        WorkingHours = "09:00–18:00 (Пн, Ср, Чт, Сб, Вс), 09:00–21:45 (Пт)",
                        AdmissionCost = 17.00m,
                        CityId = paris.Id // Связываем с Парижем
                    },
                     new Attraction
                    {
                        Name = "Эйфелева башня",
                        Description = "Металлическая башня в центре Парижа, символ Франции.",
                        History = "Построена Гюставом Эйфелем для Всемирной выставки 1889 года.",
                        PhotoUrl = "/images/eiffel_tower.jpg",
                        WorkingHours = "09:30–22:45",
                        AdmissionCost = 26.80m, // Примерная цена подъема на лифте
                        CityId = paris.Id
                    }
                );

                context.SaveChanges();
            }
        }
    }
} 