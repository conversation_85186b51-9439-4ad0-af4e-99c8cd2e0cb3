@model TravelGuide.Models.City

@{
    ViewData["Title"] = "Удаление города";
}

<h1>Удаление</h1>

<h3>Вы уверены, что хотите удалить этот город?</h3>
<div>
    <h4>Город</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Region)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Region)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Population)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Population)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.History)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.History)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CoatOfArmsUrl)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CoatOfArmsUrl)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.PhotoUrl)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PhotoUrl)
        </dd>
    </dl>

    <form asp-action="Delete" class="mt-3">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Удалить" class="btn btn-danger" />
        <a asp-action="Index" class="btn btn-secondary">Назад к списку</a>
    </form>
</div>
