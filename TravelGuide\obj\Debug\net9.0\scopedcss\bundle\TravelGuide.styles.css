/* _content/TravelGuide/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-qso4s2br5z] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-qso4s2br5z] {
  color: #0077cc;
}

.btn-primary[b-qso4s2br5z] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-qso4s2br5z], .nav-pills .show > .nav-link[b-qso4s2br5z] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-qso4s2br5z] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-qso4s2br5z] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-qso4s2br5z] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-qso4s2br5z] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-qso4s2br5z] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
