@model IEnumerable<TravelGuide.Models.City>

@{
    ViewData["Title"] = "Список городов";
}

<h1>Список городов</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">Добавить новый город</a>
</p>

<div class="row">
    @foreach (var item in Model)
    {
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                @if (!string.IsNullOrEmpty(item.PhotoUrl))
                {
                    <img src="@Url.Content(item.PhotoUrl)" class="card-img-top" alt="Фото @item.Name" style="height: 200px; object-fit: cover;">
                }
                else
                {
                    <img src="/images/placeholder.png" class="card-img-top" alt="Нет фото" style="height: 200px; object-fit: cover;">
                }
                <div class="card-body">
                    <h5 class="card-title">@item.Name</h5>
                    <p class="card-text">
                        <strong>Регион:</strong> @item.Region<br />
                        <strong>Население:</strong> @item.Population.ToString("N0")
                    </p>
                    <div class="card-text text-truncate mb-2" style="max-height: 60px;">
                        @item.History
                    </div>
                </div>
                <div class="card-footer">
                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">Подробнее</a>
                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">Редактировать</a>
                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">Удалить</a>
                </div>
            </div>
        </div>
    }
</div>
