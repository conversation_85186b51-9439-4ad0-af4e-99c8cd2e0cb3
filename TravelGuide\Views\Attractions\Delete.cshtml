@model TravelGuide.Models.Attraction

@{
    ViewData["Title"] = "Удаление достопримечательности";
}

<h1>Удаление</h1>

<h3>Вы уверены, что хотите удалить эту достопримечательность?</h3>
<div>
    <h4>Достопримечательность</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Description)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Description)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.History)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.History)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.PhotoUrl)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PhotoUrl)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.WorkingHours)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.WorkingHours)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AdmissionCost)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AdmissionCost)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.City)
        </dt>
        <dd class = "col-sm-10">
            <a asp-controller="Cities" asp-action="Details" asp-route-id="@Model.CityId">
                @Html.DisplayFor(model => model.City.Name)
            </a>
        </dd>
    </dl>

    <form asp-action="Delete" class="mt-3">
        <input type="hidden" asp-for="Id" />
        <input type="submit" value="Удалить" class="btn btn-danger" />
        <a asp-action="Index" class="btn btn-secondary">Назад к списку</a>
    </form>
</div>
